package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateOrderRequest struct {
	Name         string                       `json:"name" validate:"required,min=2,max=100"`
	Code         string                       `json:"code" validate:"required,min=2,max=50"`
	Description  string                       `json:"description"`
	LineCode     string                       `json:"line_code" validate:"required,min=2,max=50"`
	IsConfirmed  bool                         `json:"is_confirmed"`
	TemplateID   uint64                       `json:"template_id" validate:"required"`
	Duration     uint64                       `json:"duration"`
	OrderDomains []CreateOrderDomainInRequest `json:"order_domains,omitempty"`
}

type CreateOrderDomainInRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	IsAvailable bool   `json:"is_available"`
}

type UpdateOrderConfirmationRequest struct {
	IsConfirmed bool `json:"is_confirmed"`
}

type UpdateOrderRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Code        string `json:"code" validate:"required,min=2,max=50"`
	Description string `json:"description"`
	LineCode    string `json:"line_code" validate:"required,min=2,max=50"`
	IsConfirmed bool   `json:"is_confirmed"`
	UserID      uint64 `json:"user_id" validate:"required"`
	TemplateID  uint64 `json:"template_id" validate:"required"`
	Duration    uint64 `json:"duration"`
}

type OrderListItemResponse struct {
	ID           uint64                        `json:"id"`
	CreatedAt    time.Time                     `json:"created_at"`
	UpdatedAt    time.Time                     `json:"updated_at"`
	Name         string                        `json:"name"`
	Code         string                        `json:"code"`
	Description  string                        `json:"description"`
	LineCode     string                        `json:"line_code"`
	IsConfirmed  bool                          `json:"is_confirmed"`
	UserID       uint64                        `json:"user_id"`
	TemplateID   uint64                        `json:"template_id"`
	Duration     uint64                        `json:"duration"`
	User         *UserRelationResponse         `json:"user,omitempty"`
	Template     *NamespaceRelationResponse    `json:"template,omitempty"`
	OrderDomains []OrderDomainRelationResponse `json:"order_domains,omitempty"`
	Projects     []ProjectResponse             `json:"projects,omitempty"`
}

type OrderDetailResponse struct {
	ID           uint64                      `json:"id"`
	CreatedAt    time.Time                   `json:"created_at"`
	UpdatedAt    time.Time                   `json:"updated_at"`
	Name         string                      `json:"name"`
	Code         string                      `json:"code"`
	Description  string                      `json:"description"`
	LineCode     string                      `json:"line_code"`
	IsConfirmed  bool                        `json:"is_confirmed"`
	UserID       uint64                      `json:"user_id"`
	TemplateID   uint64                      `json:"template_id"`
	Duration     uint64                      `json:"duration"`
	User         *UserRelationResponse       `json:"user,omitempty"`
	Template     *NamespaceRelationResponse  `json:"template,omitempty"`
	OrderDomains []OrderDomainDetailResponse `json:"order_domains,omitempty"`
	Projects     []ProjectResponse           `json:"projects,omitempty"`
}

type OrderRelationResponse struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	LineCode    string `json:"line_code"`
	IsConfirmed bool   `json:"is_confirmed"`
	Duration    uint64 `json:"duration"`
}

type ProjectResponse struct {
	ID          uint64                       `json:"id"`
	Name        string                       `json:"name"`
	Slug        string                       `json:"slug"`
	IsActive    bool                         `json:"is_active"`
	Type        domain.NamespaceType         `json:"type"`
	IngressSpec []ProjectIngressSpecResponse `json:"ingress_spec,omitempty"`
}

type ProjectIngressResponse struct {
	ID    uint64 `json:"id"`
	Name  string `json:"name"`
	Class string `json:"class"`
}

type ProjectIngressSpecResponse struct {
	ID   uint64 `json:"id"`
	Host string `json:"host"`
	Path string `json:"path"`
	Port uint64 `json:"port"`
}

// Convert response

func ToOrderListItemDTO(d *domain.Order) *OrderListItemResponse {
	if d == nil {
		return nil
	}

	resp := &OrderListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Code:        d.Code,
		Description: d.Description,
		LineCode:    d.LineCode,
		IsConfirmed: d.IsConfirmed,
		UserID:      d.UserID,
		TemplateID:  d.TemplateID,
		Duration:    d.Duration,
		User:        ToUserRelationDTO(d.User),
		Template:    ToNamespaceRelationDTO(d.Template),
	}

	// Convert order domains if they exist
	if d.OrderDomains != nil {
		var orderDomains []OrderDomainRelationResponse
		for _, od := range d.OrderDomains {
			orderDomains = append(orderDomains, *ToOrderDomainRelationDTO(&od))
		}
		resp.OrderDomains = orderDomains
	}

	// Convert order namespaces to projects if they exist
	if d.OrderNamespaces != nil {
		var projects []ProjectResponse
		for _, on := range d.OrderNamespaces {
			if on.Namespace != nil {
				project := ProjectResponse{
					ID:       on.Namespace.ID,
					Name:     on.Namespace.Name,
					Slug:     on.Namespace.Slug,
					IsActive: on.Namespace.IsActive,
					Type:     on.Namespace.Type,
				}

				// Convert ingress data
				if on.Namespace.Ingress != nil {
					var ingressSpecs []ProjectIngressSpecResponse

					for _, ing := range on.Namespace.Ingress {
						// Collect all ingress specs from this ingress
						if ing.IngressSpecs != nil {
							for _, spec := range ing.IngressSpecs {
								ingressSpecs = append(ingressSpecs, ProjectIngressSpecResponse{
									ID:   spec.ID,
									Host: spec.Host,
									Path: spec.Path,
									Port: spec.Port,
								})
							}
						}
					}

					project.IngressSpec = ingressSpecs
				}

				projects = append(projects, project)
			}
		}
		resp.Projects = projects
	}

	return resp
}

func ToOrderDetailDTO(d *domain.Order) *OrderDetailResponse {
	if d == nil {
		return nil
	}

	resp := &OrderDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Code:        d.Code,
		Description: d.Description,
		LineCode:    d.LineCode,
		IsConfirmed: d.IsConfirmed,
		UserID:      d.UserID,
		TemplateID:  d.TemplateID,
		Duration:    d.Duration,
		User:        ToUserRelationDTO(d.User),
		Template:    ToNamespaceRelationDTO(d.Template),
	}

	if d.OrderDomains != nil {
		var orderDomains []OrderDomainDetailResponse
		for _, od := range d.OrderDomains {
			orderDomains = append(orderDomains, *ToOrderDomainDetailDTO(&od))
		}
		resp.OrderDomains = orderDomains
	}

	if d.OrderNamespaces != nil {
		var projects []ProjectResponse
		for _, on := range d.OrderNamespaces {
			if on.Namespace != nil {
				project := ProjectResponse{
					ID:       on.Namespace.ID,
					Name:     on.Namespace.Name,
					Slug:     on.Namespace.Slug,
					IsActive: on.Namespace.IsActive,
					Type:     on.Namespace.Type,
				}

				// Convert ingress data
				if on.Namespace.Ingress != nil {
					var ingressSpecs []ProjectIngressSpecResponse

					for _, ing := range on.Namespace.Ingress {
						// Collect all ingress specs from this ingress
						if ing.IngressSpecs != nil {
							for _, spec := range ing.IngressSpecs {
								ingressSpecs = append(ingressSpecs, ProjectIngressSpecResponse{
									ID:   spec.ID,
									Host: spec.Host,
									Path: spec.Path,
									Port: spec.Port,
								})
							}
						}
					}

					project.IngressSpec = ingressSpecs
				}

				projects = append(projects, project)
			}
		}
		resp.Projects = projects
	}

	return resp
}

func ToOrderRelationDTO(d *domain.Order) *OrderRelationResponse {
	if d == nil {
		return nil
	}
	return &OrderRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		Code:        d.Code,
		Description: d.Description,
		LineCode:    d.LineCode,
		IsConfirmed: d.IsConfirmed,
		Duration:    d.Duration,
	}
}
